#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动打开绑卡链接脚本
从绑卡.txt文件中读取链接并在浏览器中打开指定数量的链接
"""

import webbrowser
import time
import os
import sys

# 配置参数
LINKS_TO_OPEN = 1  # 硬编码：每次运行要打开的链接数量
DATA_FILE = "绑卡.txt"  # 数据文件名
DELAY_BETWEEN_OPENS = 1  # 打开链接之间的延迟（秒）

def read_links_from_file(filename):
    """
    从文件中读取邮箱和链接数据
    返回: [(email, link), ...]
    """
    links_data = []
    
    if not os.path.exists(filename):
        print(f"错误：文件 {filename} 不存在！")
        return links_data
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if not line:  # 跳过空行
                    continue
                
                if '----' in line:
                    parts = line.split('----', 1)  # 只分割第一个 ----
                    if len(parts) == 2:
                        email = parts[0].strip()
                        link = parts[1].strip()
                        if email and link:
                            links_data.append((email, link))
                        else:
                            print(f"警告：第 {line_num} 行数据不完整，跳过")
                    else:
                        print(f"警告：第 {line_num} 行格式不正确，跳过")
                else:
                    print(f"警告：第 {line_num} 行缺少分隔符 '----'，跳过")
    
    except Exception as e:
        print(f"读取文件时出错：{e}")
    
    return links_data

def open_links_in_browser(links_data, num_to_open):
    """
    在浏览器中打开指定数量的链接
    """
    if not links_data:
        print("没有可用的链接数据！")
        return
    
    # 确保不超过可用链接数量
    actual_num = min(num_to_open, len(links_data))
    
    print(f"准备打开 {actual_num} 个链接...")
    print("-" * 50)
    
    for i in range(actual_num):
        email, link = links_data[i]
        print(f"正在打开第 {i+1} 个链接...")
        print(f"邮箱: {email}")
        print(f"链接: {link[:50]}..." if len(link) > 50 else f"链接: {link}")
        
        try:
            # 在默认浏览器中打开链接
            webbrowser.open(link)
            print(f"✓ 成功打开第 {i+1} 个链接")
        except Exception as e:
            print(f"✗ 打开第 {i+1} 个链接失败：{e}")
        
        # 在打开下一个链接前稍作延迟
        if i < actual_num - 1:
            print(f"等待 {DELAY_BETWEEN_OPENS} 秒后打开下一个链接...")
            time.sleep(DELAY_BETWEEN_OPENS)
        
        print("-" * 30)
    
    print(f"完成！共打开了 {actual_num} 个链接")

def main():
    """
    主函数
    """
    print("=" * 60)
    print("自动打开绑卡链接脚本")
    print("=" * 60)
    
    # 检查数据文件是否存在
    if not os.path.exists(DATA_FILE):
        print(f"错误：数据文件 {DATA_FILE} 不存在！")
        print("请确保文件存在并包含正确格式的数据。")
        input("按回车键退出...")
        return
    
    # 读取链接数据
    print(f"正在读取数据文件: {DATA_FILE}")
    links_data = read_links_from_file(DATA_FILE)
    
    if not links_data:
        print("没有找到有效的链接数据！")
        input("按回车键退出...")
        return
    
    print(f"找到 {len(links_data)} 个有效链接")
    print(f"配置为打开 {LINKS_TO_OPEN} 个链接")
    
    # 确认是否继续
    try:
        confirm = input(f"\n是否继续打开 {min(LINKS_TO_OPEN, len(links_data))} 个链接？(y/n): ").lower().strip()
        if confirm not in ['y', 'yes', '是', '']:
            print("操作已取消")
            return
    except KeyboardInterrupt:
        print("\n操作已取消")
        return
    
    # 打开链接
    open_links_in_browser(links_data, LINKS_TO_OPEN)
    
    print("\n脚本执行完成！")
    input("按回车键退出...")

if __name__ == "__main__":
    main()
